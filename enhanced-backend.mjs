#!/usr/bin/env node

/**
 * MEV Arbitrage Bot - Enhanced Backend with Database Integration
 * =============================================================
 *
 * This backend integrates with:
 * - Supabase for structured data (trades, opportunities, performance)
 * - InfluxDB for time-series data (prices, metrics, analytics)
 * - Redis for caching and real-time data
 * - PostgreSQL for local persistent storage
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import { InfluxDB, Point } from '@influxdata/influxdb-client';
import Redis from 'redis';

// Load environment variables
dotenv.config();

console.log('🚀 Starting Enhanced MEV Arbitrage Bot Backend...');

const app = express();
const PORT = process.env.PORT || 8080;

// Middleware - Fix CORS for all origins during development
app.use(cors({
  origin: true, // Allow all origins during development
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// Serve static files (frontend)
app.use(express.static('.', {
  index: 'index.html',
  setHeaders: (res, path) => {
    if (path.endsWith('.html')) {
      res.setHeader('Content-Type', 'text/html');
    }
  }
}));

// Request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Database connections
let supabase = null;
let influxDB = null;
let influxWriteApi = null;
let influxQueryApi = null;
let redisClient = null;

// Connection status
const connectionStatus = {
  supabase: false,
  influxdb: false,
  redis: false
};

// Initialize Supabase
function initializeSupabase() {
  try {
    if (process.env.SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
      supabase = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        }
      );
      connectionStatus.supabase = true;
      console.log('✅ Supabase connected successfully');
    } else {
      console.log('⚠️  Supabase credentials not found, using mock data');
    }
  } catch (error) {
    console.error('❌ Supabase connection failed:', error.message);
  }
}

// Initialize InfluxDB
function initializeInfluxDB() {
  try {
    if (process.env.INFLUXDB_URL && process.env.INFLUXDB_TOKEN) {
      influxDB = new InfluxDB({
        url: process.env.INFLUXDB_URL,
        token: process.env.INFLUXDB_TOKEN,
      });

      influxWriteApi = influxDB.getWriteApi(
        process.env.INFLUXDB_ORG || 'Dev-KE',
        process.env.INFLUXDB_BUCKET || 'mev-monitoring',
        'ns'
      );

      influxQueryApi = influxDB.getQueryApi(process.env.INFLUXDB_ORG || 'Dev-KE');

      // Configure write options
      influxWriteApi.useDefaultTags({
        application: 'mev-arbitrage-bot',
        environment: process.env.NODE_ENV || 'development'
      });

      connectionStatus.influxdb = true;
      console.log('✅ InfluxDB connected successfully');
    } else {
      console.log('⚠️  InfluxDB credentials not found, metrics disabled');
    }
  } catch (error) {
    console.error('❌ InfluxDB connection failed:', error.message);
  }
}

// Initialize Redis
async function initializeRedis() {
  try {
    redisClient = Redis.createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379',
      socket: {
        reconnectStrategy: (retries) => Math.min(retries * 50, 500)
      }
    });

    redisClient.on('error', (err) => {
      console.error('Redis error:', err);
      connectionStatus.redis = false;
    });

    redisClient.on('connect', () => {
      console.log('✅ Redis connected successfully');
    });

    redisClient.on('ready', () => {
      connectionStatus.redis = true;
      console.log('✅ Redis ready for operations');
    });

    redisClient.on('end', () => {
      connectionStatus.redis = false;
      console.log('⚠️ Redis connection ended');
    });

    await redisClient.connect();

    // Test the connection
    const pong = await redisClient.ping();
    if (pong === 'PONG') {
      console.log('✅ Redis ping test successful');
      connectionStatus.redis = true;
    }

  } catch (error) {
    console.error('❌ Redis connection failed:', error.message);
    connectionStatus.redis = false;
  }
}

// Mock data (fallback when databases are not available)
const mockOpportunities = [
  {
    id: 'opp_1',
    type: 'intra-chain',
    assets: ['ETH', 'USDC'],
    exchanges: ['Uniswap', 'SushiSwap'],
    potential_profit: 125.50,
    profit_percentage: 2.1,
    timestamp: new Date().toISOString(),
    network: 'ethereum',
    confidence: 85,
    slippage: 0.5,
    created_at: new Date().toISOString()
  },
  {
    id: 'opp_2',
    type: 'triangular',
    assets: ['WBTC', 'ETH', 'USDC'],
    exchanges: ['Balancer', 'Curve', 'Uniswap'],
    potential_profit: 89.25,
    profit_percentage: 1.8,
    timestamp: new Date(Date.now() - 30000).toISOString(),
    network: 'ethereum',
    confidence: 78,
    slippage: 0.8,
    created_at: new Date(Date.now() - 30000).toISOString()
  },
  {
    id: 'opp_3',
    type: 'cross-chain',
    assets: ['USDC', 'USDT'],
    exchanges: ['Polygon-Uniswap', 'Ethereum-Curve'],
    potential_profit: 45.75,
    profit_percentage: 0.9,
    timestamp: new Date(Date.now() - 60000).toISOString(),
    network: 'polygon',
    confidence: 92,
    slippage: 0.3,
    created_at: new Date(Date.now() - 60000).toISOString()
  }
];

const mockTrades = [
  {
    id: 'trade_1',
    opportunity_id: 'opp_1',
    type: 'intra-chain',
    assets: ['ETH', 'USDC'],
    exchanges: ['Uniswap', 'SushiSwap'],
    executed_profit: 118.25,
    gas_fees: 7.25,
    status: 'success',
    timestamp: new Date(Date.now() - 300000).toISOString(),
    network: 'ethereum',
    tx_hash: '0x1234567890abcdef',
    created_at: new Date(Date.now() - 300000).toISOString()
  },
  {
    id: 'trade_2',
    opportunity_id: 'opp_2',
    type: 'triangular',
    assets: ['WBTC', 'ETH', 'USDC'],
    exchanges: ['Balancer', 'Curve', 'Uniswap'],
    executed_profit: 82.50,
    gas_fees: 12.75,
    status: 'success',
    timestamp: new Date(Date.now() - 600000).toISOString(),
    network: 'ethereum',
    tx_hash: '0xabcdef1234567890',
    created_at: new Date(Date.now() - 600000).toISOString()
  },
  {
    id: 'trade_3',
    opportunity_id: 'opp_3',
    type: 'cross-chain',
    assets: ['USDC', 'USDT'],
    exchanges: ['Polygon-Uniswap', 'Ethereum-Curve'],
    executed_profit: 42.30,
    gas_fees: 3.45,
    status: 'success',
    timestamp: new Date(Date.now() - 900000).toISOString(),
    network: 'polygon',
    tx_hash: '0x9876543210fedcba',
    created_at: new Date(Date.now() - 900000).toISOString()
  }
];

const mockTokens = [
  {
    id: 'ethereum_******************************************',
    name: 'Ethereum',
    symbol: 'ETH',
    address: '******************************************',
    liquidity: 1000000,
    safety_score: 100,
    is_whitelisted: true,
    network: 'ethereum',
    decimals: 18,
    total_supply: '120000000000000000000000000',
    last_updated: new Date().toISOString()
  },
  {
    id: 'ethereum_0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505',
    name: 'USD Coin',
    symbol: 'USDC',
    address: '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505',
    liquidity: 500000,
    safety_score: 95,
    is_whitelisted: true,
    network: 'ethereum',
    decimals: 6,
    total_supply: '50000000000000',
    last_updated: new Date().toISOString()
  },
  {
    id: 'ethereum_******************************************',
    name: 'Wrapped Bitcoin',
    symbol: 'WBTC',
    address: '******************************************',
    liquidity: 750000,
    safety_score: 98,
    is_whitelisted: true,
    network: 'ethereum',
    decimals: 8,
    total_supply: '21000000000000',
    last_updated: new Date().toISOString()
  }
];

// Helper function to write metrics to InfluxDB
async function writeMetricToInflux(measurement, tags, fields) {
  if (!influxWriteApi) return;

  try {
    const point = new Point(measurement);

    Object.entries(tags).forEach(([key, value]) => {
      point.tag(key, value);
    });

    Object.entries(fields).forEach(([key, value]) => {
      if (typeof value === 'number') {
        point.floatField(key, value);
      } else if (typeof value === 'boolean') {
        point.booleanField(key, value);
      } else {
        point.stringField(key, value.toString());
      }
    });

    influxWriteApi.writePoint(point);
    await influxWriteApi.flush();
  } catch (error) {
    console.error('Error writing to InfluxDB:', error);
  }
}

// Routes
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    services: {
      backend: true,
      supabase: connectionStatus.supabase,
      influxdb: connectionStatus.influxdb,
      redis: connectionStatus.redis
    },
    databases: connectionStatus
  });
});

// Opportunities endpoint with database integration
app.get('/api/opportunities', async (req, res) => {
  const limit = parseInt(req.query.limit) || 20;

  try {
    let opportunities = [];

    if (supabase && connectionStatus.supabase) {
      // Try to get from Supabase first
      const { data, error } = await supabase
        .from('opportunities')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (!error && data && data.length > 0) {
        opportunities = data;
      } else {
        // Fallback to mock data and save to Supabase
        opportunities = mockOpportunities.slice(0, limit);

        // Save mock opportunities to Supabase for future requests
        for (const opp of opportunities) {
          await supabase.from('opportunities').upsert([opp]);
        }
      }
    } else {
      // Use mock data
      opportunities = mockOpportunities.slice(0, limit);
    }

    // Write metrics to InfluxDB
    if (influxWriteApi) {
      for (const opp of opportunities) {
        await writeMetricToInflux('opportunities', {
          type: opp.type,
          network: opp.network,
          exchange: opp.exchanges[0] || 'unknown'
        }, {
          profit: opp.potential_profit,
          profitPercentage: opp.profit_percentage,
          confidence: opp.confidence
        });
      }
    }

    res.json({
      success: true,
      data: opportunities,
      count: opportunities.length,
      source: connectionStatus.supabase ? 'database' : 'mock'
    });

  } catch (error) {
    console.error('Error fetching opportunities:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch opportunities',
      data: mockOpportunities.slice(0, limit)
    });
  }
});

// Trades endpoint with database integration
app.get('/api/trades', async (req, res) => {
  const limit = parseInt(req.query.limit) || 50;

  try {
    let trades = [];

    if (supabase && connectionStatus.supabase) {
      const { data, error } = await supabase
        .from('trades')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (!error && data && data.length > 0) {
        trades = data;
      } else {
        trades = mockTrades.slice(0, limit);

        // Save mock trades to Supabase
        for (const trade of trades) {
          await supabase.from('trades').upsert([trade]);
        }
      }
    } else {
      trades = mockTrades.slice(0, limit);
    }

    // Write trade metrics to InfluxDB
    if (influxWriteApi) {
      for (const trade of trades) {
        await writeMetricToInflux('trades', {
          type: trade.type,
          network: trade.network,
          exchange: trade.exchanges[0] || 'unknown',
          success: trade.status === 'success' ? 'true' : 'false'
        }, {
          profit: trade.executed_profit,
          gasFees: trade.gas_fees,
          executionTime: 1000, // Mock execution time
          success: trade.status === 'success'
        });
      }
    }

    res.json({
      success: true,
      data: trades,
      count: trades.length,
      source: connectionStatus.supabase ? 'database' : 'mock'
    });

  } catch (error) {
    console.error('Error fetching trades:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch trades',
      data: mockTrades.slice(0, limit)
    });
  }
});

// Tokens endpoint with database integration
app.get('/api/tokens', async (req, res) => {
  try {
    let tokens = [];

    if (supabase && connectionStatus.supabase) {
      const { data, error } = await supabase
        .from('tokens')
        .select('*')
        .order('last_updated', { ascending: false });

      if (!error && data && data.length > 0) {
        tokens = data;
      } else {
        tokens = mockTokens;

        // Save mock tokens to Supabase
        for (const token of tokens) {
          await supabase.from('tokens').upsert([token]);
        }
      }
    } else {
      tokens = mockTokens;
    }

    // Write price metrics to InfluxDB
    if (influxWriteApi) {
      for (const token of tokens) {
        await writeMetricToInflux('price_data', {
          symbol: token.symbol,
          exchange: 'mock',
          network: token.network
        }, {
          price: Math.random() * 1000 + 100, // Mock price
          volume24h: token.liquidity,
          change24h: (Math.random() - 0.5) * 10
        });
      }
    }

    res.json({
      success: true,
      data: tokens,
      count: tokens.length,
      source: connectionStatus.supabase ? 'database' : 'mock'
    });

  } catch (error) {
    console.error('Error fetching tokens:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch tokens',
      data: mockTokens
    });
  }
});

// Analytics endpoint with database integration
app.get('/api/analytics/performance', async (req, res) => {
  try {
    let metrics = {
      totalTrades: 45,
      successfulTrades: 39,
      totalProfit: 2850.75,
      netProfit: 2650.50,
      winRate: 86.7,
      avgProfit: 67.96,
      dailyVolume: 125000
    };

    if (supabase && connectionStatus.supabase) {
      // Try to get real performance metrics
      const { data, error } = await supabase
        .from('performance_metrics')
        .select('*')
        .order('date', { ascending: false })
        .limit(1);

      if (!error && data && data.length > 0) {
        const latest = data[0];
        metrics = {
          totalTrades: latest.total_trades,
          successfulTrades: latest.successful_trades,
          totalProfit: latest.total_profit,
          netProfit: latest.net_profit,
          winRate: latest.win_rate,
          avgProfit: latest.avg_profit,
          dailyVolume: latest.daily_volume
        };
      } else {
        // Save mock metrics to Supabase
        await supabase.from('performance_metrics').upsert([{
          total_trades: metrics.totalTrades,
          successful_trades: metrics.successfulTrades,
          failed_trades: metrics.totalTrades - metrics.successfulTrades,
          total_profit: metrics.totalProfit,
          total_loss: 200.25,
          net_profit: metrics.netProfit,
          win_rate: metrics.winRate,
          avg_profit: metrics.avgProfit,
          avg_loss: 25.03,
          profit_factor: 2.5,
          sharpe_ratio: 1.8,
          max_drawdown: 5.2,
          roi: 15.3,
          daily_volume: metrics.dailyVolume,
          date: new Date().toISOString().split('T')[0]
        }]);
      }
    }

    // Write system metrics to InfluxDB
    if (influxWriteApi) {
      await writeMetricToInflux('system_metrics', {
        metric: 'performance'
      }, {
        totalTrades: metrics.totalTrades,
        successfulTrades: metrics.successfulTrades,
        totalProfit: metrics.totalProfit,
        winRate: metrics.winRate,
        dailyVolume: metrics.dailyVolume
      });
    }

    res.json({
      success: true,
      data: metrics,
      source: connectionStatus.supabase ? 'database' : 'mock'
    });

  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch analytics',
      data: {
        totalTrades: 45,
        successfulTrades: 39,
        totalProfit: 2850.75,
        netProfit: 2650.50,
        winRate: 86.7,
        avgProfit: 67.96,
        dailyVolume: 125000
      }
    });
  }
});

// System health endpoint
app.get('/api/system/health', async (req, res) => {
  try {
    const healthData = {
      isHealthy: true,
      emergencyStop: false,
      riskMetrics: {
        totalExposure: 0,
        dailyPnL: 250.75,
        maxDrawdown: 0,
        volatility: 15.2,
        winRate: 86.7
      },
      activeAlerts: 0,
      criticalAlerts: 0,
      databases: connectionStatus,
      uptime: process.uptime()
    };

    // Write health metrics to InfluxDB
    if (influxWriteApi) {
      await writeMetricToInflux('system_health', {
        status: 'healthy'
      }, {
        uptime: process.uptime(),
        dailyPnL: healthData.riskMetrics.dailyPnL,
        winRate: healthData.riskMetrics.winRate,
        supabaseConnected: connectionStatus.supabase ? 1 : 0,
        influxdbConnected: connectionStatus.influxdb ? 1 : 0,
        redisConnected: connectionStatus.redis ? 1 : 0
      });
    }

    res.json({
      success: true,
      data: healthData
    });

  } catch (error) {
    console.error('Error fetching system health:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch system health'
    });
  }
});

// Real-time data simulation endpoint
app.get('/api/realtime/update', async (req, res) => {
  try {
    // Simulate real-time updates
    const updates = {
      newOpportunities: Math.floor(Math.random() * 3),
      priceUpdates: Math.floor(Math.random() * 10),
      systemLoad: Math.random() * 100,
      timestamp: new Date().toISOString()
    };

    // Cache in Redis if available
    if (redisClient && connectionStatus.redis) {
      await redisClient.setEx('realtime:updates', 30, JSON.stringify(updates));
    }

    res.json({
      success: true,
      data: updates
    });

  } catch (error) {
    console.error('Error generating realtime updates:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate updates'
    });
  }
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: err.message
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    path: req.url
  });
});

// Initialize all database connections
async function initializeDatabases() {
  console.log('🔌 Initializing database connections...');

  initializeSupabase();
  initializeInfluxDB();
  await initializeRedis();

  console.log('📊 Database connection status:');
  console.log(`   • Supabase: ${connectionStatus.supabase ? '✅' : '❌'}`);
  console.log(`   • InfluxDB: ${connectionStatus.influxdb ? '✅' : '❌'}`);
  console.log(`   • Redis: ${connectionStatus.redis ? '✅' : '❌'}`);
}

// Start server
async function startServer() {
  try {
    await initializeDatabases();

    app.listen(PORT, '127.0.0.1', () => {
      console.log(`\n✅ Enhanced Backend server running on http://localhost:${PORT}`);
      console.log(`✅ Health check: http://localhost:${PORT}/health`);
      console.log(`✅ API endpoints available:`);
      console.log(`   - GET /api/opportunities`);
      console.log(`   - GET /api/trades`);
      console.log(`   - GET /api/tokens`);
      console.log(`   - GET /api/analytics/performance`);
      console.log(`   - GET /api/system/health`);
      console.log(`   - GET /api/realtime/update`);
      console.log(`🔄 Ready to serve frontend requests with database integration...`);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 Shutting down enhanced backend...');

  if (influxWriteApi) {
    try {
      await influxWriteApi.close();
    } catch (error) {
      console.error('Error closing InfluxDB:', error);
    }
  }

  if (redisClient) {
    try {
      await redisClient.quit();
    } catch (error) {
      console.error('Error closing Redis:', error);
    }
  }

  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🛑 Shutting down enhanced backend...');

  if (influxWriteApi) {
    try {
      await influxWriteApi.close();
    } catch (error) {
      console.error('Error closing InfluxDB:', error);
    }
  }

  if (redisClient) {
    try {
      await redisClient.quit();
    } catch (error) {
      console.error('Error closing Redis:', error);
    }
  }

  process.exit(0);
});

// Start the server
startServer();
