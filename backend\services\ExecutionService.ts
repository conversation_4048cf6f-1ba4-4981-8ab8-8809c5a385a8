import { EventEmitter } from 'events';
import { ethers } from 'ethers';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { ArbitrageOpportunity, ArbitrageType } from './OpportunityDetectionService.js';

export enum TradeStatus {
  PENDING = 'pending',
  EXECUTING = 'executing',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface Trade {
  id: string;
  opportunityId: string;
  type: ArbitrageType;
  assets: string[];
  exchanges: string[];
  executedProfit: number;
  gasFees: number;
  status: TradeStatus;
  timestamp: number;
  network: string;
  txHash?: string;
  errorMessage?: string;
  executionTime?: number;
}

export interface ExecutionResult {
  success: boolean;
  trade?: Trade;
  error?: string;
  txHash?: string;
}

export class ExecutionService extends EventEmitter {
  private providers: Map<string, ethers.JsonRpcProvider> = new Map();
  private wallets: Map<string, ethers.Wallet> = new Map();
  private executionQueue: ArbitrageOpportunity[] = [];
  private activeTrades: Map<string, Trade> = new Map();
  private isRunning = false;
  private isPaused = false;
  private executionInterval: NodeJS.Timeout | null = null;

  // Execution parameters
  private maxConcurrentTrades = 3;
  private gasMultiplier = parseFloat(config.GAS_PRICE_MULTIPLIER) / 100;
  private maxSlippage = parseFloat(config.MAX_SLIPPAGE);

  constructor() {
    super();
    this.initializeProviders();
    this.initializeWallets();
  }

  private initializeProviders() {
    this.providers.set('ethereum', new ethers.JsonRpcProvider(config.ETHEREUM_RPC_URL));
    this.providers.set('polygon', new ethers.JsonRpcProvider(config.POLYGON_RPC_URL));
    this.providers.set('bsc', new ethers.JsonRpcProvider(config.BSC_RPC_URL));
  }

  private initializeWallets() {
    if (config.PRIVATE_KEY) {
      try {
        this.providers.forEach((provider, network) => {
          const wallet = new ethers.Wallet(config.PRIVATE_KEY!, provider);
          this.wallets.set(network, wallet);
        });
        logger.info('Wallets initialized for trading');
      } catch (error) {
        logger.error('Failed to initialize wallets:', error);
      }
    } else {
      logger.warn('No private key configured - trading will be simulated');
    }
  }

  public async start() {
    if (this.isRunning) return;

    logger.info('Starting Execution Service...');
    this.isRunning = true;
    this.isPaused = false;

    // Start execution loop
    this.executionInterval = setInterval(() => {
      this.processExecutionQueue();
    }, 1000); // Process queue every second

    logger.info('Execution Service started');
  }

  public async stop() {
    if (!this.isRunning) return;

    logger.info('Stopping Execution Service...');
    this.isRunning = false;

    if (this.executionInterval) {
      clearInterval(this.executionInterval);
      this.executionInterval = null;
    }

    // Wait for active trades to complete or timeout
    await this.waitForActiveTrades();

    logger.info('Execution Service stopped');
  }

  public pause() {
    this.isPaused = true;
    logger.info('Execution Service paused');
  }

  public resume() {
    this.isPaused = false;
    logger.info('Execution Service resumed');
  }

  public async evaluateOpportunity(opportunity: ArbitrageOpportunity) {
    try {
      // Quick profitability check
      if (opportunity.potentialProfit < parseFloat(config.MIN_PROFIT_THRESHOLD)) {
        logger.debug(`Opportunity ${opportunity.id} below profit threshold`);
        return;
      }

      // Check if we're already trading this pair
      const isAlreadyTrading = Array.from(this.activeTrades.values()).some(trade =>
        trade.assets.some(asset => opportunity.assets.includes(asset)) &&
        trade.status === TradeStatus.EXECUTING
      );

      if (isAlreadyTrading) {
        logger.debug(`Already trading similar assets for opportunity ${opportunity.id}`);
        return;
      }

      // Add to execution queue
      this.executionQueue.push(opportunity);
      logger.info(`Opportunity ${opportunity.id} added to execution queue`);

    } catch (error) {
      logger.error('Error evaluating opportunity:', error);
    }
  }

  private async processExecutionQueue() {
    if (!this.isRunning || this.isPaused || this.executionQueue.length === 0) {
      return;
    }

    // Check if we can execute more trades
    const activeTradingCount = Array.from(this.activeTrades.values())
      .filter(trade => trade.status === TradeStatus.EXECUTING).length;

    if (activeTradingCount >= this.maxConcurrentTrades) {
      return;
    }

    // Get the most profitable opportunity
    const opportunity = this.executionQueue.shift();
    if (!opportunity) return;

    // Check if opportunity is still valid (not too old)
    const age = Date.now() - opportunity.timestamp;
    if (age > 10000) { // 10 seconds
      logger.debug(`Opportunity ${opportunity.id} too old, skipping`);
      return;
    }

    // Execute the trade
    await this.executeTrade(opportunity);
  }

  private async executeTrade(opportunity: ArbitrageOpportunity): Promise<ExecutionResult> {
    const tradeId = `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const trade: Trade = {
      id: tradeId,
      opportunityId: opportunity.id,
      type: opportunity.type,
      assets: opportunity.assets,
      exchanges: opportunity.exchanges,
      executedProfit: 0,
      gasFees: 0,
      status: TradeStatus.PENDING,
      timestamp: Date.now(),
      network: opportunity.network
    };

    this.activeTrades.set(tradeId, trade);
    this.emit('trade', trade);

    try {
      logger.info(`Executing ${opportunity.type} trade ${tradeId} for ${opportunity.assets.join('/')}`);
      
      // Update status to executing
      trade.status = TradeStatus.EXECUTING;
      this.emit('tradeUpdate', trade);

      let result: ExecutionResult;

      switch (opportunity.type) {
        case ArbitrageType.INTRA_CHAIN:
          result = await this.executeIntraChainArbitrage(opportunity, trade);
          break;
        case ArbitrageType.CROSS_CHAIN:
          result = await this.executeCrossChainArbitrage(opportunity, trade);
          break;
        case ArbitrageType.TRIANGULAR:
          result = await this.executeTriangularArbitrage(opportunity, trade);
          break;
        default:
          throw new Error(`Unsupported arbitrage type: ${opportunity.type}`);
      }

      if (result.success) {
        trade.status = TradeStatus.SUCCESS;
        trade.txHash = result.txHash;
        trade.executionTime = Date.now() - trade.timestamp;
        logger.info(`Trade ${tradeId} executed successfully with profit $${trade.executedProfit.toFixed(2)}`);
      } else {
        trade.status = TradeStatus.FAILED;
        trade.errorMessage = result.error;
        logger.warn(`Trade ${tradeId} failed: ${result.error}`);
      }

      this.emit('tradeUpdate', trade);
      return result;

    } catch (error) {
      logger.error(`Error executing trade ${tradeId}:`, error);
      
      trade.status = TradeStatus.FAILED;
      trade.errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.emit('tradeUpdate', trade);

      return {
        success: false,
        error: trade.errorMessage
      };
    }
  }

  private async executeIntraChainArbitrage(
    opportunity: ArbitrageOpportunity, 
    trade: Trade
  ): Promise<ExecutionResult> {
    try {
      const wallet = this.wallets.get(opportunity.network);
      if (!wallet) {
        return { success: false, error: 'No wallet configured for network' };
      }

      // Simulate trade execution for demo
      // In production, this would interact with actual smart contracts
      
      const simulationResult = await this.simulateIntraChainTrade(opportunity);
      if (!simulationResult.success) {
        return simulationResult;
      }

      // Execute the actual trade
      if (config.NODE_ENV === 'production' && config.PRIVATE_KEY) {
        // Real execution would happen here
        // For demo, we'll simulate
      }

      // Simulate successful execution
      const executedProfit = opportunity.potentialProfit * (0.9 + Math.random() * 0.2); // ±10% variation
      const gasFees = opportunity.estimatedGas * 20 * 1e-9 * 2000; // Estimated gas cost

      trade.executedProfit = executedProfit - gasFees;
      trade.gasFees = gasFees;

      return {
        success: true,
        trade,
        txHash: `0x${Math.random().toString(16).substr(2, 64)}`
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Execution failed'
      };
    }
  }

  private async simulateIntraChainTrade(opportunity: ArbitrageOpportunity): Promise<ExecutionResult> {
    try {
      // Simulate trade validation
      await new Promise(resolve => setTimeout(resolve, 100)); // Simulate network delay

      // Check if opportunity is still profitable
      const currentProfitability = opportunity.potentialProfit * (0.8 + Math.random() * 0.4);
      
      if (currentProfitability < parseFloat(config.MIN_PROFIT_THRESHOLD)) {
        return {
          success: false,
          error: 'Opportunity no longer profitable'
        };
      }

      // Simulate slippage check
      if (opportunity.slippage > this.maxSlippage) {
        return {
          success: false,
          error: 'Slippage too high'
        };
      }

      return { success: true };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Simulation failed'
      };
    }
  }

  private async executeCrossChainArbitrage(
    opportunity: ArbitrageOpportunity, 
    trade: Trade
  ): Promise<ExecutionResult> {
    try {
      // Cross-chain arbitrage is more complex and involves bridging
      // This is a simplified implementation
      
      logger.info(`Executing cross-chain arbitrage for ${opportunity.assets.join('/')}`);
      
      // Simulate cross-chain execution
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate longer execution time
      
      const executedProfit = opportunity.potentialProfit * 0.85; // Account for bridge costs
      const gasFees = opportunity.estimatedGas * 1.5; // Higher gas for cross-chain

      trade.executedProfit = executedProfit - gasFees;
      trade.gasFees = gasFees;

      return {
        success: true,
        trade,
        txHash: `0x${Math.random().toString(16).substr(2, 64)}`
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Cross-chain execution failed'
      };
    }
  }

  private async executeTriangularArbitrage(
    opportunity: ArbitrageOpportunity, 
    trade: Trade
  ): Promise<ExecutionResult> {
    try {
      logger.info(`Executing triangular arbitrage for ${opportunity.assets.join('/')}`);
      
      // Simulate triangular arbitrage execution
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const executedProfit = opportunity.potentialProfit * 0.9; // Account for multiple swaps
      const gasFees = opportunity.estimatedGas * 3; // Higher gas for multiple swaps

      trade.executedProfit = executedProfit - gasFees;
      trade.gasFees = gasFees;

      return {
        success: true,
        trade,
        txHash: `0x${Math.random().toString(16).substr(2, 64)}`
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Triangular execution failed'
      };
    }
  }

  private async waitForActiveTrades() {
    const timeout = 30000; // 30 seconds timeout
    const startTime = Date.now();

    while (this.activeTrades.size > 0 && Date.now() - startTime < timeout) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (this.activeTrades.size > 0) {
      logger.warn(`${this.activeTrades.size} trades still active after timeout`);
    }
  }

  public getTrades(): Trade[] {
    return Array.from(this.activeTrades.values())
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  public getTrade(id: string): Trade | undefined {
    return this.activeTrades.get(id);
  }

  public getQueueSize(): number {
    return this.executionQueue.length;
  }

  public getActiveTradesCount(): number {
    return Array.from(this.activeTrades.values())
      .filter(trade => trade.status === TradeStatus.EXECUTING).length;
  }

  public isHealthy(): boolean {
    return this.isRunning && !this.isPaused;
  }

  public getStats() {
    const statusStats: Record<string, number> = {};
    this.activeTrades.forEach(trade => {
      statusStats[trade.status] = (statusStats[trade.status] || 0) + 1;
    });

    const totalProfit = Array.from(this.activeTrades.values())
      .filter(trade => trade.status === TradeStatus.SUCCESS)
      .reduce((sum, trade) => sum + trade.executedProfit, 0);

    const successfulTrades = Array.from(this.activeTrades.values())
      .filter(trade => trade.status === TradeStatus.SUCCESS).length;

    const totalTrades = this.activeTrades.size;
    const successRate = totalTrades > 0 ? (successfulTrades / totalTrades) * 100 : 0;

    return {
      totalTrades,
      successfulTrades,
      successRate,
      totalProfit,
      queueSize: this.executionQueue.length,
      activeTrades: this.getActiveTradesCount(),
      statusStats,
      isRunning: this.isRunning,
      isPaused: this.isPaused
    };
  }
}
