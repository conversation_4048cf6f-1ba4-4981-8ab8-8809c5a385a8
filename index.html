<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MEV Arbitrage Bot - Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: #1F2937;
            color: #D1D5DB;
            font-family: system-ui, -apple-system, sans-serif;
        }
        .card {
            background: #374151;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .status-success { background: #10B981; color: #065F46; }
        .status-error { background: #EF4444; color: #991B1B; }
        .status-warning { background: #FBBF24; color: #92400E; }
        .status-info { background: #3B82F6; color: #1E3A8A; }
        .grid { display: grid; gap: 1rem; }
        .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
        .grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
        .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
        .text-center { text-align: center; }
        .text-3xl { font-size: 1.875rem; }
        .text-xl { font-size: 1.25rem; }
        .text-lg { font-size: 1.125rem; }
        .text-sm { font-size: 0.875rem; }
        .text-xs { font-size: 0.75rem; }
        .font-bold { font-weight: 700; }
        .font-medium { font-weight: 500; }
        .mb-2 { margin-bottom: 0.5rem; }
        .mb-4 { margin-bottom: 1rem; }
        .mb-6 { margin-bottom: 1.5rem; }
        .p-3 { padding: 0.75rem; }
        .p-6 { padding: 1.5rem; }
        .px-4 { padding-left: 1rem; padding-right: 1rem; }
        .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
        .rounded-lg { border-radius: 0.5rem; }
        .rounded-md { border-radius: 0.375rem; }
        .max-h-96 { max-height: 24rem; }
        .overflow-y-auto { overflow-y: auto; }
        .text-green-400 { color: #34D399; }
        .text-blue-400 { color: #60A5FA; }
        .text-yellow-400 { color: #FBBF24; }
        .text-purple-400 { color: #A78BFA; }
        .text-gray-400 { color: #9CA3AF; }
        .text-gray-300 { color: #D1D5DB; }
        .bg-gray-600 { background-color: #4B5563; }
        .bg-gray-500 { background-color: #6B7280; }
        .hover\:bg-gray-500:hover { background-color: #6B7280; }
        .transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; }
        .flex { display: flex; }
        .justify-between { justify-content: space-between; }
        .items-center { align-items: center; }
        .min-h-screen { min-height: 100vh; }
        .max-w-7xl { max-width: 80rem; }
        .mx-auto { margin-left: auto; margin-right: auto; }
    </style>
</head>
<body>
    <div class="min-h-screen p-6">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-3xl font-bold mb-6">MEV Arbitrage Bot Dashboard</h1>

            <div id="status" class="px-4 py-2 rounded-lg mb-4 status-info">
                Loading backend data...
            </div>

            <!-- KPIs -->
            <div class="grid grid-cols-4 mb-6">
                <div class="card text-center">
                    <h3 class="text-sm font-medium text-gray-400 mb-2">TOTAL PROFIT</h3>
                    <p class="text-3xl font-bold text-green-400" id="total-profit">$0.00</p>
                </div>
                <div class="card text-center">
                    <h3 class="text-sm font-medium text-gray-400 mb-2">WIN RATE</h3>
                    <p class="text-3xl font-bold text-blue-400" id="win-rate">0.0%</p>
                </div>
                <div class="card text-center">
                    <h3 class="text-sm font-medium text-gray-400 mb-2">TOTAL TRADES</h3>
                    <p class="text-3xl font-bold text-yellow-400" id="total-trades">0</p>
                </div>
                <div class="card text-center">
                    <h3 class="text-sm font-medium text-gray-400 mb-2">DAILY VOLUME</h3>
                    <p class="text-3xl font-bold text-purple-400" id="daily-volume">$0</p>
                </div>
            </div>

            <!-- Data Sections -->
            <div class="grid grid-cols-3">
                <!-- Opportunities -->
                <div class="card">
                    <h2 class="text-xl font-bold mb-4" id="opportunities-title">
                        Live Opportunities (0)
                    </h2>
                    <div class="max-h-96 overflow-y-auto" id="opportunities-list">
                        <p class="text-gray-400 text-center py-8">Loading...</p>
                    </div>
                </div>

                <!-- Recent Trades -->
                <div class="card">
                    <h2 class="text-xl font-bold mb-4" id="trades-title">
                        Recent Trades (0)
                    </h2>
                    <div class="max-h-96 overflow-y-auto" id="trades-list">
                        <p class="text-gray-400 text-center py-8">Loading...</p>
                    </div>
                </div>

                <!-- Tokens -->
                <div class="card">
                    <h2 class="text-xl font-bold mb-4" id="tokens-title">
                        Monitored Tokens (0)
                    </h2>
                    <div class="max-h-96 overflow-y-auto" id="tokens-list">
                        <p class="text-gray-400 text-center py-8">Loading...</p>
                    </div>
                </div>
            </div>

            <!-- Debug Info -->
            <div class="card">
                <h2 class="text-xl font-bold mb-4">Debug Information</h2>
                <pre id="debug-info" class="text-xs bg-gray-800 p-4 rounded overflow-auto max-h-64"></pre>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';
        let debugLog = [];

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push(`[${timestamp}] ${message}`);
            document.getElementById('debug-info').textContent = debugLog.slice(-20).join('\n');
            console.log(message);
        }

        function updateStatus(status, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `px-4 py-2 rounded-lg mb-4 status-${status}`;
            statusEl.textContent = message;
        }

        function renderOpportunities(opportunities) {
            const container = document.getElementById('opportunities-list');
            document.getElementById('opportunities-title').textContent = `Live Opportunities (${opportunities.length})`;

            if (opportunities.length === 0) {
                container.innerHTML = '<p class="text-gray-400 text-center py-8">No opportunities detected</p>';
                return;
            }

            container.innerHTML = opportunities.map(opp => `
                <div class="p-3 bg-gray-600 hover:bg-gray-500 rounded-md transition-colors mb-2">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-blue-400">${opp.type}</span>
                        <span class="text-xs text-gray-400">${opp.network}</span>
                    </div>
                    <p class="text-sm text-gray-300">${opp.assets.join(' / ')}</p>
                    <p class="text-sm text-gray-400">${opp.exchanges.join(' & ')}</p>
                    <p class="text-lg font-bold text-green-400">$${opp.potentialProfit.toFixed(2)}</p>
                </div>
            `).join('');
        }

        function renderTrades(trades) {
            const container = document.getElementById('trades-list');
            document.getElementById('trades-title').textContent = `Recent Trades (${trades.length})`;

            if (trades.length === 0) {
                container.innerHTML = '<p class="text-gray-400 text-center py-8">No trades executed</p>';
                return;
            }

            container.innerHTML = trades.map(trade => {
                const statusColor = trade.status === 'success' ? 'text-green-400' :
                                   trade.status === 'failed' ? 'text-red-400' : 'text-yellow-400';
                return `
                    <div class="p-3 bg-gray-600 hover:bg-gray-500 rounded-md transition-colors mb-2">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-purple-400">${trade.type}</span>
                            <span class="text-sm ${statusColor}">${trade.status.toUpperCase()}</span>
                        </div>
                        <p class="text-sm text-gray-300">${trade.assets.join(' / ')}</p>
                        <div class="flex justify-between">
                            <p class="text-lg font-bold text-green-400">+$${trade.executedProfit.toFixed(2)}</p>
                            <p class="text-sm text-gray-400">Gas: $${trade.gasFees.toFixed(2)}</p>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function renderTokens(tokens) {
            const container = document.getElementById('tokens-list');
            document.getElementById('tokens-title').textContent = `Monitored Tokens (${tokens.length})`;

            if (tokens.length === 0) {
                container.innerHTML = '<p class="text-gray-400 text-center py-8">No tokens configured</p>';
                return;
            }

            container.innerHTML = tokens.map(token => `
                <div class="p-3 bg-gray-600 rounded-md mb-2">
                    <div class="flex justify-between">
                        <span class="font-medium">${token.symbol}</span>
                        <span class="text-sm text-gray-400">${token.network}</span>
                    </div>
                    <div class="text-sm text-gray-300">${token.name}</div>
                    <div class="text-xs text-gray-400">Safety: ${token.safetyScore}/100</div>
                </div>
            `).join('');
        }

        async function loadData() {
            try {
                log('Loading data from backend...');
                updateStatus('info', 'Loading backend data...');

                // Check backend health
                const healthResponse = await fetch(`${API_BASE}/health`);
                if (!healthResponse.ok) throw new Error('Backend not available');

                const health = await healthResponse.json();
                log(`Backend health: ${health.status}`);

                // Load all data
                const [oppRes, tradesRes, tokensRes, metricsRes] = await Promise.all([
                    fetch(`${API_BASE}/api/opportunities`),
                    fetch(`${API_BASE}/api/trades`),
                    fetch(`${API_BASE}/api/tokens`),
                    fetch(`${API_BASE}/api/analytics/performance`)
                ]);

                const [oppData, tradesData, tokensData, metricsData] = await Promise.all([
                    oppRes.json(),
                    tradesRes.json(),
                    tokensRes.json(),
                    metricsRes.json()
                ]);

                const opportunities = oppData.success ? oppData.data : [];
                const trades = tradesData.success ? tradesData.data : [];
                const tokens = tokensData.success ? tokensData.data : [];
                const metrics = metricsData.success ? metricsData.data : null;

                log(`Loaded: ${opportunities.length} opportunities, ${trades.length} trades, ${tokens.length} tokens`);

                // Update UI
                renderOpportunities(opportunities);
                renderTrades(trades);
                renderTokens(tokens);

                // Update metrics
                if (metrics) {
                    document.getElementById('total-profit').textContent = `$${metrics.netProfit.toFixed(2)}`;
                    document.getElementById('win-rate').textContent = `${metrics.winRate.toFixed(1)}%`;
                    document.getElementById('total-trades').textContent = metrics.totalTrades;
                    document.getElementById('daily-volume').textContent = `$${metrics.dailyVolume.toLocaleString()}`;
                }

                updateStatus('success', `✅ Connected - ${opportunities.length} opportunities, ${trades.length} trades, ${tokens.length} tokens`);
                log('All data loaded successfully');

            } catch (err) {
                log(`Error loading data: ${err.message}`);
                updateStatus('error', `Error: ${err.message}`);
            }
        }

        // Initialize
        log('Initializing MEV Arbitrage Bot Dashboard...');
        loadData();

        // Refresh data every 30 seconds
        setInterval(loadData, 30000);
    </script>
</body>
</html>
