{"name": "mev-arbitrage-bot", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "echo 'Frontend: Open index.html in browser' && echo 'Frontend available at: file://$(pwd)/index.html'", "dev:backend": "node working-backend.mjs", "build": "npm run build:backend", "build:backend": "tsc -p backend/tsconfig.json", "start": "node working-backend.mjs", "start:backend": "node working-backend.mjs", "start:full": "node start-full-system.mjs", "start:enhanced": "node enhanced-backend.mjs", "verify": "node verify-system.mjs", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:contracts": "hardhat test", "test:integration": "jest --testPathPattern=tests/integration", "test:performance": "jest --testPathPattern=tests/performance", "test:e2e": "jest --testPathPattern=tests/e2e", "test:all": "npm run test:contracts && npm run test && npm run test:integration && npm run test:performance", "compile:contracts": "hardhat compile", "deploy:contracts": "hardhat run scripts/deploy.ts", "benchmark": "node tests/performance/benchmark.js"}, "dependencies": {"@google/genai": "^1.1.0", "@influxdata/influxdb-client": "^1.35.0", "@supabase/supabase-js": "^2.49.8", "autoprefixer": "^10.4.21", "axios": "^1.6.2", "cors": "^2.8.5", "decimal.js": "^10.4.3", "dotenv": "^16.5.0", "ethers": "^6.8.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "lodash": "^4.17.21", "node-cron": "^3.0.3", "postcss": "^8.5.3", "redis": "^4.6.10", "tailwindcss": "^4.1.7", "uuid": "^9.0.1", "web3": "^4.2.2", "winston": "^3.11.0", "ws": "^8.14.2", "zod": "^3.22.4"}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^4.0.0", "@openzeppelin/contracts": "^5.0.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/lodash": "^4.14.202", "@types/node": "^22.14.0", "@types/node-cron": "^3.0.11", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "chai": "^4.3.10", "concurrently": "^8.2.2", "hardhat": "^2.19.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "~5.7.2"}}